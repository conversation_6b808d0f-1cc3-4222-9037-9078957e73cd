# Multi-Strategy Trading System Configuration
# Main configuration file for shared resources and strategy coordination

# ============================================================================
# STRATEGY CONFIGURATION (modify these for different strategy combinations)
# ============================================================================

# Strategy weights and enablement
strategies:
  stat_arb_carry_trade:
    enabled: true                # Enable/disable this strategy
    weight: 0.33                 # Portfolio weight for this strategy (will be normalized)
  cross_sectional_momentum:
    enabled: true               # Cross-sectional momentum strategy
    weight: 0.33                 # Portfolio weight when enabled
  trend_trading:
    enabled: true               # Trend trading strategy
    weight: 0.34                 # Portfolio weight when enabled

# Portfolio combination settings
portfolio_combination:
  enable_position_netting: true  # Net long/short positions by symbol
  min_position_size_usd: 10.0    # Minimum position size after combination
  position_rounding_decimals: 6  # Decimal places for position rounding

  # Portfolio-level volatility targeting
  enable_portfolio_vol_targeting: true  # Enable portfolio-level volatility targeting
  portfolio_target_volatility: 0.20     # Target portfolio volatility (20% annualized)
  default_correlation: 0.30              # Default correlation between assets for portfolio vol calculation

# Performance tracking settings
performance_tracking:
  max_performance_history_days: 90        # Days of performance history to keep
  enable_detailed_performance_tracking: true  # Track detailed position-level data
  performance_tracking_file: "performance_history.json"  # Performance data file

# Basic metrics system settings (ESSENTIAL METRICS ONLY)
basic_metrics:
  enable_basic_metrics: true             # Enable basic metrics system
  metrics_config_file: "config/metrics_config.yaml"  # Basic metrics configuration
  risk_free_rate: 0.02                   # Annual risk-free rate for Sharpe ratio (2%)
  metrics_calculation_frequency_hours: 6  # Calculate metrics every 6 hours
  enable_real_time_tracking: true        # Enable real-time metrics calculation
  metrics_database_path: "data/basic_portfolio_metrics.db"  # SQLite database path

  # Basic metrics to track (ENABLED)
  track_pnl_curve: true                  # PnL Curve - Cumulative arithmetic returns
  track_sharpe_ratio: true               # Sharpe Ratio - Risk-adjusted returns
  track_calmar_ratio: true               # Calmar Ratio - Return/max drawdown ratio
  track_annualized_returns: true         # Annualized Returns - Arithmetic annual return
  track_max_drawdown: true               # Max Drawdown - Peak-to-trough decline
  track_max_recovery_time: true          # Max Time to Recovery - Days to recover
  track_portfolio_exposures: true        # Portfolio Ranking & Exposures - Signal strength
  track_performance_attribution: true    # Performance Attribution - Long vs short
  track_turnover: true                   # Turnover - Portfolio turnover rate
  track_capacity_utilization: true       # Capacity Utilization - Capital deployment
  track_slippage: true                   # Slippage - Execution cost analysis

  # Long-term data storage (FOR INVESTOR TRACK RECORDS)
  data_retention_years: 5                # Keep 5 years of data for investor reports
  enable_automatic_backup: true          # Enable automatic database backups
  backup_frequency_days: 7               # Backup every 7 days
  backup_retention_count: 12             # Keep 12 backups (3 months)
  backup_path: "backups/metrics/"        # Backup directory

  # Database optimization for long-term storage
  enable_database_compression: true      # Enable data compression
  enable_wal_mode: true                  # Enable Write-Ahead Logging for performance
  vacuum_frequency_days: 30              # Vacuum database monthly for optimization

  # Grafana integration (BASIC)
  enable_grafana_export: true            # Enable Grafana data export
  grafana_export_frequency_hours: 6      # Export data every 6 hours
  grafana_export_path: "grafana/data/"   # Grafana export directory
  grafana_dashboard_config: "grafana/basic_dashboard_config.json"  # Basic dashboard

# ============================================================================
# FREQUENTLY CHANGED PARAMETERS (modify these for different trading scenarios)
# ============================================================================

# Control Flags (most commonly toggled)
simulation_mode: true             # Enable simulation mode (no real trades)
immediate_start: true             # Start immediately without waiting
debug_mode: false                 # Enable debug logging

# Exchange Configuration
exchange: bybit                   # Supported: bybit, binance, okx, hyperliquid
use_testnet: false               # Use testnet for testing
use_demo: true                   # Use demo trading (if supported)

# Shared Strategy Parameters (used by all strategies unless overridden)
total_capital_usd: 10000         # Total capital to deploy across all strategies

# Shared risk management settings
buffer_zone_tolerance_percentage: 5.0  # Buffer zone around target position (% of individual coin's USD position)

# Shared beta projection settings (used by strategies that enable it)
enable_beta_projection: false    # Disabled by default since beta adjustment is done in feature calculation
market_index_symbol: "BTCUSDT"   # Market index symbol for beta calculation (BTC as crypto market proxy)
beta_neutrality_tolerance: 0.05  # Maximum allowed portfolio beta deviation from zero (±5%)
beta_optimization_max_weight_change: 0.20  # Maximum allowed weight change during beta optimization (20%)

# ============================================================================
# EXECUTION STYLE CONFIGURATION (conservative to avoid API bans)
# ============================================================================

# Execution style affects rate limiting and order placement behavior
preferred_execution_style: "conservative"  # conservative, balanced, aggressive (now reasonably conservative)

# Conservative execution parameters (reasonably conservative to avoid API rate limits)
conservative_execution:
  max_concurrent_coins: 3        # Moderate concurrent execution
  max_concurrent_batches: 6      # Moderate batch concurrency
  batch_semaphore_size: 3        # Max 3 batches per coin simultaneously
  coin_start_delay_ms: 500       # 0.5 second delay between starting each coin
  batch_start_delay_ms: 300      # 0.3 second delay between batches
  order_placement_delay_ms: 200  # 0.2 second delay between orders
  max_orders_per_second: 3       # Conservative but reasonable order rate
  max_orders_per_minute: 120     # Conservative minute limit
  batch_delay_ms: 500            # 0.5 second delay between order batches
  retry_delay_ms: 1500           # 1.5 second delay before retrying

# ============================================================================
# STRATEGY MANAGEMENT PARAMETERS (adjust occasionally)
# ============================================================================

# Multi-strategy execution settings
max_concurrent_strategies: 3     # Moderate for reasonably conservative execution
strategy_timeout_seconds: 300    # Reasonable timeout (5 minutes)

# ============================================================================
# MODERATELY CHANGED PARAMETERS (adjust occasionally)
# ============================================================================

# API Credentials - Add your keys here (or use environment variables)
api_key: ""                      # Your API key (or set BYBIT_API_KEY env var)
api_secret: ""                   # Your API secret (or set BYBIT_API_SECRET env var)

# Additional credentials for other exchanges:
# For OKX (uncomment and fill when using OKX):
# passphrase: "your_okx_passphrase"                  # OKX requires passphrase

# For Hyperliquid (uncomment and fill when using Hyperliquid):
# wallet_address: "******************************************"  # Your wallet address
# private_key: "your_private_key_here"               # Your private key (keep secure!)

# Shared filtering parameters (used by all strategies unless overridden)
exclude_new_listings_days: 60    # Exclude coins listed within this many days
min_historical_data_days: 60     # Minimum days of historical data required

# Shared execution settings (used by all strategies)
min_orderbook_depth: 3           # Minimum orderbook depth required
max_spread_threshold: 0.05       # Maximum spread threshold (5%)
monitor_position_alignment: false # Check position alignment during monitoring (optional)


# Randomized Batch Execution Settings (reasonably conservative for API safety)
min_batch_size: 3                # Moderate batch count
max_batch_size: 5                # Moderate batch count
min_delta_percentage: 15         # Moderate chunks to balance efficiency and API calls
max_delta_percentage: 40         # Moderate chunks to balance efficiency and API calls
min_batch_interval_seconds: 60   # Moderate delays between batches
max_batch_interval_seconds: 300  # Moderate maximum delays
min_orderbook_levels: 3          # Moderate orderbook levels
max_orderbook_levels: 6          # Moderate orderbook levels
max_execution_iterations: 8      # Reasonable iterations to balance completion and API calls
order_settle_time_seconds: 300   # Reasonable settle time (5 minutes)
aggressive_execution_mode: false # Keep post-only for conservative execution

# Delta Reconciliation Configuration
delta_tolerance_percentage: 5.0  # Position tolerance for delta reconciliation (% of individual position value)

# Post-Only Order Optimization Settings (reasonably conservative)
post_only_max_retries: 3         # Reasonable retries to balance success and API calls
post_only_retry_delay_ms: 400    # Moderate delay between retries
order_price_offset_pct: 0.001    # Small offset to improve fill rates (0.1%)

# ============================================================================
# RARELY CHANGED PARAMETERS (advanced settings, usually keep defaults)
# ============================================================================

# Timing Configuration
# Note: Rebalancing occurs at 23:00 UTC daily (1 hour before funding)
# Monitoring occurs at 23:00, 07:00, 15:00 UTC (1 hour before each funding time)
max_sleep_seconds: 3600          # Maximum sleep duration between checks (1 hour)
default_sleep_seconds: 3600      # Default sleep when no events scheduled (1 hour)
monitoring_window_seconds: 300   # Window around monitoring time to trigger check (5 minutes)

# Cache Configuration
cache_default_ttl: 300           # Default cache TTL in seconds (5 minutes)
cache_max_size: 1000             # Maximum cache entries
cache_gc_interval: 300           # Garbage collection interval in seconds (5 minutes)

# Data Analysis
default_volatility: 0.2          # Default volatility fallback (20%)
default_beta: 1.0                # Default beta fallback (1.0 = market beta)

# Beta Calculation Settings (advanced)
beta_calculation_days: 60        # Number of days for rolling beta calculation
beta_cache_ttl: 3600            # Beta cache TTL in seconds (1 hour)

# Performance Optimization Flags (conservative settings)
async_batch_execution: false     # Disabled for conservative execution to avoid simultaneous API calls

# Logging Configuration
log_file: "multi_strategy_trading.log"  # Log file name (will be placed in logs/ directory)

# Performance and Caching (reasonably conservative settings to avoid API limits)
max_concurrent_api_calls: 5      # Moderate concurrent API calls
rate_limit_delay_ms: 300         # Moderate delay between API calls
cache_ttl_seconds: 300           # Moderate cache TTL to balance freshness and API calls
symbol_processing_batch_size: 30 # Moderate batches to balance efficiency and API load
cache_cleanup_interval: 300      # Cache cleanup interval in seconds
order_history_retention_days: 7  # Order history retention in days

# ============================================================================
# DEFAULT PARAMETERS (system defaults - rarely need modification)
# ============================================================================

# Default values for all system parameters
defaults:
  # Core strategy defaults
  default_volatility: 0.20            # Default volatility fallback (20%)
  default_beta: 1.0                    # Default beta fallback (1.0 = market beta)

  # Data validation defaults
  max_price_deviation: 0.10            # Maximum price deviation for validation (10%)
  min_volume_threshold: 1000           # Minimum volume threshold for validation

  # Memory management defaults
  max_memory_usage_mb: 512             # Maximum memory usage in MB
  memory_check_interval: 300           # Memory check interval in seconds
  memory_cleanup_threshold: 0.8        # Memory cleanup threshold (80%)

  # Performance optimization defaults (conservative)
  concurrent_calculation_batch_size: 5  # Reduced batch size for conservative execution
  cleanup_interval_seconds: 3600       # Cleanup interval for long-running processes (1 hour)

# ============================================================================
# TECHNICAL/ADVANCED PARAMETERS (conservative settings to avoid API bans)
# ============================================================================

# Execution Rate Limiting (reasonably conservative settings)
execution_rate_limiting:
  # Reasonably conservative async batch execution settings
  async_batches_per_coin: false  # Keep disabled for conservative execution
  max_concurrent_coins: 3        # Moderate concurrent execution
  max_concurrent_batches: 6      # Moderate batch concurrency
  batch_semaphore_size: 3        # Maximum 3 batches per coin

  # Reasonably conservative staggered execution delays
  coin_start_delay_ms: 500       # 0.5 second delay between coins
  batch_start_delay_ms: 300      # 0.3 second delay between batches
  order_placement_delay_ms: 200  # 0.2 second delay between orders

  # Reasonably conservative execution phases
  execution_phases:
    enabled: true                # Enable phased execution for safety
    phase_size: 15               # Moderate phases to spread load
    phase_delay_seconds: 30      # 30 second delay between phases

  # Reasonably conservative emergency throttling
  emergency_throttling:
    enabled: true                # Enable emergency throttling
    rate_limit_threshold: 3      # Trigger throttling after 3 rate limit errors
    throttle_delay_seconds: 60   # 1 minute throttling delay
    max_throttle_time: 300       # Maximum 5 minute throttling

# API Rate Limiting Configuration (reasonably conservative based on official documentation)
api_rate_limiting:
  # Reasonably conservative global rate limiting (based on lowest common denominator)
  max_requests_per_second: 5     # Conservative but reasonable API request rate
  max_concurrent_requests: 3     # Moderate concurrent requests
  burst_limit: 10                # Reasonable burst limit

  # Reasonably conservative order placement rate limiting
  order_placement:
    max_orders_per_second: 3     # Conservative order rate (3 orders/sec)
    max_orders_per_minute: 120   # Conservative minute limit (2 orders/sec average)
    batch_delay_ms: 500          # 0.5 second delay between order batches
    retry_delay_ms: 1500         # 1.5 second delay before retrying
    max_retries: 3               # Reasonable retries

  # Exchange-specific rate limits (based on official documentation, reasonably conservative)
  exchange_limits:
    bybit: 5                     # Conservative for Bybit (official: 10/s for default users)
    binance: 15                  # Conservative for Binance (official: ~20/s based on weight limits)
    okx: 8                       # Conservative for OKX (official: varies by endpoint, 6-20/s)
    hyperliquid: 15              # Conservative for Hyperliquid (official: 20/s equivalent)

  # Reasonably conservative adaptive rate limiting for safety
  adaptive_limiting:
    enabled: true                # Enable adaptive rate limiting
    backoff_multiplier: 2.0      # Moderate backoff on rate limit hit
    recovery_time_seconds: 30    # Moderate recovery time
    max_backoff_seconds: 300     # Maximum 5 minute backoff

  # Reasonably conservative error handling and recovery
  error_handling:
    # Reasonably conservative retry configuration
    max_retries: 3               # Reasonable retries to balance success and API calls
    rate_limit_max_retries: 4    # Reasonable rate limit retries
    network_max_retries: 3       # Reasonable network retries

    # Reasonably conservative exponential backoff
    retry_base_delay: 1.0        # Moderate base delay
    retry_max_delay: 300.0       # Maximum delay in seconds (5 minutes)
    retry_multiplier: 2.0        # Moderate backoff multiplier
    retry_jitter: true           # Add random jitter to delays

    # Reasonably conservative network timeout configuration
    connect_timeout: 30          # Moderate connection timeout
    read_timeout: 60             # Moderate read timeout
    total_timeout: 180           # Moderate total request timeout (3 minutes)

    # Reasonably conservative connection pool configuration
    max_connections: 30          # Moderate connection pool
    max_keepalive_connections: 10 # Moderate keep-alive connections
    keepalive_expiry: 30         # Moderate keep-alive expiry

  # Reasonably conservative circuit breaker for API failures
  circuit_breaker:
    failure_threshold: 5         # Moderate threshold before circuit opens
    timeout_seconds: 60          # Moderate circuit breaker timeout
    half_open_max_calls: 3       # Moderate calls in half-open state

# ============================================================================
# CCXT CONFIGURATION (enableRateLimit and exchange-specific settings)
# ============================================================================

# CCXT enableRateLimit is set to True in exchange initialization
# Note: CCXT's built-in rate limiting works for sequential calls but NOT for
# simultaneous/asynchronous calls. Our custom rate limiting handles this.
ccxt_config:
  enable_rate_limit: true        # Enable CCXT's built-in rate limiting
  timeout: 30000                 # 30 second timeout for CCXT requests
  rateLimit: 500                 # Minimum delay between requests (0.5 second, reasonably conservative)

  # Exchange-specific CCXT options (based on official documentation)
  bybit_options:
    defaultType: 'swap'          # Use perpetual futures
    recvWindow: 20000            # 20 second receive window (Bybit official recommendation)
    adjustForTimeDifference: true # Adjust for time differences
    rateLimit: 200               # 200ms between requests (5 req/sec)

  binance_options:
    defaultType: 'future'        # Use futures for perpetual contracts
    recvWindow: 5000             # 5 second receive window (Binance default)
    adjustForTimeDifference: true # Adjust for time differences
    rateLimit: 100               # 100ms between requests (10 req/sec, conservative)

  okx_options:
    defaultType: 'swap'          # Use swap for perpetual futures
    recvWindow: 20000            # 20 second receive window
    adjustForTimeDifference: true # Adjust for time differences
    rateLimit: 150               # 150ms between requests (~6.7 req/sec, conservative)

  hyperliquid_options:
    rateLimit: 100               # 100ms between requests (10 req/sec, conservative)
    timeout: 30000               # 30 second timeout

  # Additional exchange-specific parameters for robustness
  exchange_specific_params:
    bybit:
      max_retries: 3             # Maximum retries for failed requests
      retry_delay: 1000          # Delay between retries (ms)
      connection_timeout: 30000  # Connection timeout (ms)

    binance:
      max_retries: 3             # Maximum retries for failed requests
      retry_delay: 500           # Delay between retries (ms)
      connection_timeout: 30000  # Connection timeout (ms)
      weight_limit_buffer: 0.8   # Use 80% of weight limit to avoid hitting limits

    okx:
      max_retries: 3             # Maximum retries for failed requests
      retry_delay: 750           # Delay between retries (ms)
      connection_timeout: 30000  # Connection timeout (ms)

    hyperliquid:
      max_retries: 3             # Maximum retries for failed requests
      retry_delay: 500           # Delay between retries (ms)
      connection_timeout: 30000  # Connection timeout (ms)
      weight_limit_buffer: 0.9   # Use 90% of weight limit (Hyperliquid is more lenient)
